import React from 'react';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import { SelectChangeEvent } from '@mui/material/Select';
import { styled } from '@mui/material/styles';
import { InfoCard } from '@backstage/core-components';
import TestingIcon from '@mui/icons-material/BugReport';

import { ApiCollection, HttpMethod } from '../../../types';
import { CollectionsSidebar } from './CollectionsSidebar/CollectionsSidebar';
import { RequestBuilder } from './RequestBuilder';
import { ResponseViewer } from './ResponseViewer';
import { EnvironmentManager } from './EnvironmentManager';
import { ResizablePanels } from './ResizablePanels';
import {
  getContainerStyles,
  getElevatedCardStyles,
  getEnhancedSpacing,
  getEnhancedBorderRadius,
  getEnhancedShadows,
  getEnhancedGradients,
  getHeaderStyles,
} from '../../../theme';

// Enhanced styled components
const StyledContainer = styled(Box)(({ theme }) => ({
  ...getContainerStyles(theme),
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
}));

const HeaderCard = styled(Card)(({ theme }) => ({
  ...getHeaderStyles(theme),
  marginBottom: theme.spacing(4),
  position: 'relative',
  overflow: 'hidden',
}));

const HeaderContent = styled(Box)(({ theme }) => ({
  position: 'relative',
  zIndex: 1,
  padding: theme.spacing(4),
  [theme.breakpoints.down('md')]: {
    padding: theme.spacing(3),
  },
}));

const HeaderTitle = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(1),
  '& .MuiSvgIcon-root': {
    marginRight: theme.spacing(1),
    fontSize: '28px',
    color: theme.palette.primary.main,
  },
}));

const MainContentGrid = styled(Grid)(({ theme }) => ({
  flex: 1,
  minHeight: 0,
  '& .MuiGrid-item': {
    display: 'flex',
    flexDirection: 'column',
  },
}));

const RequestResponseContainer = styled(Box)(({ theme }) => ({
  height: 'calc(100vh - 320px)',
  minHeight: '600px',
  display: 'flex',
  flexDirection: 'column',
  [theme.breakpoints.down('lg')]: {
    height: 'calc(100vh - 280px)',
    minHeight: '500px',
  },
  [theme.breakpoints.down('md')]: {
    height: 'calc(100vh - 240px)',
    minHeight: '400px',
  },
}));

interface ApiTestingLayoutProps {
  // Collections props
  collections: ApiCollection[];
  collectionsLoading: boolean;
  collectionsError: string | null;
  expandedFolders: Record<string, boolean>;
  selectedItemId: string | null;
  unsavedCollections: Set<string>;
  isSaving: Record<string, boolean>;
  onFolderToggle: (folderId: string) => void;
  onItemSelect: (itemId: string) => void;
  onContextMenu: (event: React.MouseEvent, itemId: string, itemType: 'collection' | 'folder' | 'request') => void;
  onAddCollection: () => void;
  onSaveCollection: (collectionId: string) => void;
  onSaveAllCollections: () => void;

  // Environment props
  environments: any[];
  currentEnvironment: string;
  selectedEnvironmentId: string;
  environmentDialogOpen: boolean;
  onEnvironmentChange: (event: SelectChangeEvent<string>) => void;
  onOpenEnvironmentDialog: () => void;
  onCloseEnvironmentDialog: () => void;
  onOpenExportDialog: () => void;
  onEnvironmentUpdate: (environments: any[]) => void;
  onSelectedEnvironmentChange: (id: string) => void;

  // Request props
  currentRequest: any;
  currentResponse: any;
  isLoading: boolean;
  tabValue: number;
  responseTabValue: number;
  isGeneratingTests: boolean;
  isRunningTests: boolean;
  testResults: any[];
  testError: string | null;
  isSavingPreRequestScript: boolean;
  preRequestScriptError: string | null;
  onRequestChange: (request: any) => void;
  onMethodChange: (event: SelectChangeEvent<HttpMethod>) => void;
  onUrlChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  onResponseTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  onSendRequest: () => void;
  onSavePreRequestScript: () => void;
  onSaveTests: () => void;
  onRunTests: () => void;
  onGenerateTests: () => void;
}

export const ApiTestingLayout: React.FC<ApiTestingLayoutProps> = ({
  // Collections
  collections,
  collectionsLoading,
  collectionsError,
  expandedFolders,
  selectedItemId,
  unsavedCollections,
  isSaving,
  onFolderToggle,
  onItemSelect,
  onContextMenu,
  onAddCollection,
  onSaveCollection,
  onSaveAllCollections,

  // Environment
  environments,
  currentEnvironment,
  selectedEnvironmentId,
  environmentDialogOpen,
  onEnvironmentChange,
  onOpenEnvironmentDialog,
  onCloseEnvironmentDialog,
  onOpenExportDialog,
  onEnvironmentUpdate,
  onSelectedEnvironmentChange,

  // Request
  currentRequest,
  currentResponse,
  isLoading,
  tabValue,
  responseTabValue,
  isGeneratingTests,
  isRunningTests,
  testResults,
  testError,
  isSavingPreRequestScript,
  preRequestScriptError,
  onRequestChange,
  onMethodChange,
  onUrlChange,
  onTabChange,
  onResponseTabChange,
  onSendRequest,
  onSavePreRequestScript,
  onSaveTests,
  onRunTests,
  onGenerateTests,
}) => {
  return (
    <StyledContainer>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <HeaderCard>
            <HeaderContent>
              <HeaderTitle>
                <TestingIcon />
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: 'text.primary' }}>
                  API Testing
                </Typography>
              </HeaderTitle>
              <Typography variant="body1" sx={{ color: 'text.secondary', lineHeight: 1.6 }}>
                Test your APIs by sending requests and viewing responses. Organize your requests into collections and use environments to manage variables.
              </Typography>
            </HeaderContent>
          </HeaderCard>
        </Grid>

        {/* Main content grid */}
        <MainContentGrid container item xs={12} spacing={3}>
          {/* Collections sidebar */}
          <Grid item xs={12} md={3}>
            <CollectionsSidebar
              collections={collections}
              collectionsLoading={collectionsLoading}
              collectionsError={collectionsError}
              expandedFolders={expandedFolders}
              selectedItemId={selectedItemId}
              unsavedCollections={unsavedCollections}
              isSaving={isSaving}
              onFolderToggle={onFolderToggle}
              onItemSelect={onItemSelect}
              onContextMenu={onContextMenu}
              onAddCollection={onAddCollection}
              onSaveCollection={onSaveCollection}
              onSaveAllCollections={onSaveAllCollections}
            />
          </Grid>

          {/* Request and response area */}
          <Grid item xs={12} md={9}>
            {/* Environment Manager */}
            <EnvironmentManager
              environments={environments}
              currentEnvironment={currentEnvironment}
              selectedEnvironmentId={selectedEnvironmentId}
              environmentDialogOpen={environmentDialogOpen}
              onEnvironmentChange={onEnvironmentChange}
              onOpenEnvironmentDialog={onOpenEnvironmentDialog}
              onCloseEnvironmentDialog={onCloseEnvironmentDialog}
              onOpenExportDialog={onOpenExportDialog}
              onEnvironmentUpdate={onEnvironmentUpdate}
              onSelectedEnvironmentChange={onSelectedEnvironmentChange}
            />

            {/* Resizable Request and Response Panels */}
            <RequestResponseContainer>
              <ResizablePanels
                defaultTopHeight={60}
                minTopHeight={25}
                minBottomHeight={20}
                allowCollapse
                topPanel={
                  <RequestBuilder
                    currentRequest={currentRequest}
                    currentResponse={currentResponse}
                    isLoading={isLoading}
                    tabValue={tabValue}
                    currentEnvironment={environments.find(env => env.id === currentEnvironment)}
                    onRequestChange={onRequestChange}
                    onMethodChange={onMethodChange}
                    onUrlChange={onUrlChange}
                    onTabChange={onTabChange}
                    onSendRequest={onSendRequest}
                    onSavePreRequestScript={onSavePreRequestScript}
                    onSaveTests={onSaveTests}
                    onRunTests={onRunTests}
                    onGenerateTests={onGenerateTests}
                    isSavingPreRequestScript={isSavingPreRequestScript}
                    preRequestScriptError={preRequestScriptError}
                    isGeneratingTests={isGeneratingTests}
                    isRunningTests={isRunningTests}
                    testError={testError}
                  />
                }
                bottomPanel={
                  <ResponseViewer
                    response={currentResponse}
                    responseTabValue={responseTabValue}
                    testResults={testResults}
                    isRunningTests={isRunningTests}
                    testError={testError}
                    onResponseTabChange={onResponseTabChange}
                  />
                }
              />
            </RequestResponseContainer>
          </Grid>
        </MainContentGrid>
      </Grid>
    </StyledContainer>
  );
};
