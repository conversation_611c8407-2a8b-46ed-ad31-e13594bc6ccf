import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { ResizablePanels } from './ResizablePanels';

// Create a test theme for MUI components
const testTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    divider: 'rgba(0, 0, 0, 0.12)',
  },
  spacing: 8,
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
    },
  },
});

// Test wrapper component that provides theme context
const TestWrapper = ({ children }) => (
  <ThemeProvider theme={testTheme}>
    {children}
  </ThemeProvider>
);

describe('ResizablePanels', () => {
  it('renders without crashing', () => {
    const topPanel = <div data-testid="top-panel">Top Panel Content</div>;
    const bottomPanel = <div data-testid="bottom-panel">Bottom Panel Content</div>;

    render(
      <TestWrapper>
        <ResizablePanels
          topPanel={topPanel}
          bottomPanel={bottomPanel}
        />
      </TestWrapper>
    );

    expect(screen.getByTestId('top-panel')).toBeInTheDocument();
    expect(screen.getByTestId('bottom-panel')).toBeInTheDocument();
  });

  it('renders with custom height settings', () => {
    const topPanel = <div data-testid="top-panel">Top Panel Content</div>;
    const bottomPanel = <div data-testid="bottom-panel">Bottom Panel Content</div>;

    render(
      <TestWrapper>
        <ResizablePanels
          topPanel={topPanel}
          bottomPanel={bottomPanel}
          defaultTopHeight={70}
          minTopHeight={30}
          minBottomHeight={15}
          allowCollapse={false}
        />
      </TestWrapper>
    );

    expect(screen.getByTestId('top-panel')).toBeInTheDocument();
    expect(screen.getByTestId('bottom-panel')).toBeInTheDocument();
  });
});
